import { getCorsHeaders } from '@/app/lib/api';
import { authMiddleware } from '@/app/lib/auth';
import { log } from '@repo/observability/log';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';

export const config = {
  matcher: [
    // Match all API routes and health check
    '/api/:path*',
    '/health',
  ],
};

/**
 * Middleware function that handles authentication and CORS for the API
 */
export default function middleware(request: NextRequest) {
  const origin = request.headers.get('origin') ?? '';
  const corsHeaders = getCorsHeaders(origin);

  // Handle CORS preflight requests
  if (request.method === 'OPTIONS') {
    log.debug('Handling CORS preflight request', {
      origin,
      path: request.nextUrl.pathname,
    });

    return new NextResponse(null, {
      status: 204,
      headers: corsHeaders,
    });
  }

  // Apply authentication middleware
  const authResponse = authMiddleware(request);
  if (authResponse) {
    // Add CORS headers to auth error responses
    for (const [key, value] of Object.entries(corsHeaders)) {
      authResponse.headers.set(key, value);
    }
    return authResponse;
  }

  // Continue to the route handler
  const response = NextResponse.next();

  // Add CORS headers to successful responses
  for (const [key, value] of Object.entries(corsHeaders)) {
    response.headers.set(key, value);
  }

  return response;
}
