import { withAuth } from '@/app/lib/auth';
import {
  sendExchangeItemReceivedEmail,
  sendExchangeItemShippedEmail,
  sendExchangeRequestAutoApprovedEmail,
  sendExchangeRequestManualApprovedEmail,
  sendExchangeRequestManualReceivedEmail,
  sendRequestDeclinedEmail,
  sendReturnRequestAutoApprovedEmail,
  sendReturnRequestManualApprovedEmail,
  sendReturnRequestManualReceivedEmail,
} from '@repo/email';
import { type NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

const baseEmailSchema = z.object({
  to: z.string().email(),
  returnNumber: z.string().min(1),
  // Optional return label data for PDF generation
  returnLabelOption: z.string().optional(),
  orderName: z.string().optional(),
  customerName: z.string().optional(),
  customerAddress: z.string().optional(),
});

const emailSchemas = {
  'return-request-auto-approved': baseEmailSchema.extend({
    type: z.literal('return-request-auto-approved'),
    refundDays: z.string().optional(),
  }),
  'return-request-manual-received': baseEmailSchema.extend({
    type: z.literal('return-request-manual-received'),
    reviewDays: z.string().optional(),
  }),
  'return-request-manual-approved': baseEmailSchema.extend({
    type: z.literal('return-request-manual-approved'),
    refundDays: z.string().optional(),
    returnInstructions: z.string().optional(),
  }),
  'exchange-request-auto-approved': baseEmailSchema.extend({
    type: z.literal('exchange-request-auto-approved'),
  }),
  'exchange-item-shipped': baseEmailSchema.extend({
    type: z.literal('exchange-item-shipped'),
    trackingNumber: z.string().min(1),
  }),
  'exchange-item-received': baseEmailSchema.extend({
    type: z.literal('exchange-item-received'),
  }),
  'exchange-request-manual-received': baseEmailSchema.extend({
    type: z.literal('exchange-request-manual-received'),
  }),
  'exchange-request-manual-approved': baseEmailSchema.extend({
    type: z.literal('exchange-request-manual-approved'),
  }),
  'request-declined': baseEmailSchema.extend({
    type: z.literal('request-declined'),
  }),
};

const requestSchema = z.discriminatedUnion('type', [
  emailSchemas['return-request-auto-approved'],
  emailSchemas['return-request-manual-received'],
  emailSchemas['return-request-manual-approved'],
  emailSchemas['exchange-request-auto-approved'],
  emailSchemas['exchange-item-shipped'],
  emailSchemas['exchange-item-received'],
  emailSchemas['exchange-request-manual-received'],
  emailSchemas['exchange-request-manual-approved'],
  emailSchemas['request-declined'],
]);

async function postHandler(request: NextRequest) {
  try {
    const body = await request.json();
    const data = requestSchema.parse(body);

    let result: { success: boolean; error?: unknown; result?: unknown };

    switch (data.type) {
      case 'return-request-auto-approved':
        result = await sendReturnRequestAutoApprovedEmail(
          data.to,
          data.returnNumber,
          data.refundDays,
          data.returnLabelOption && data.orderName
            ? {
                returnLabelOption: data.returnLabelOption,
                orderName: data.orderName,
                customerName: data.customerName,
                customerAddress: data.customerAddress,
              }
            : undefined
        );
        break;
      case 'return-request-manual-received':
        result = await sendReturnRequestManualReceivedEmail(
          data.to,
          data.returnNumber,
          data.reviewDays
        );
        break;
      case 'return-request-manual-approved':
        result = await sendReturnRequestManualApprovedEmail(
          data.to,
          data.returnNumber,
          data.refundDays,
          data.returnInstructions,
          data.returnLabelOption && data.orderName
            ? {
                returnLabelOption: data.returnLabelOption,
                orderName: data.orderName,
                customerName: data.customerName,
                customerAddress: data.customerAddress,
              }
            : undefined
        );
        break;
      case 'exchange-request-auto-approved':
        result = await sendExchangeRequestAutoApprovedEmail(
          data.to,
          data.returnNumber,
          data.returnLabelOption && data.orderName
            ? {
                returnLabelOption: data.returnLabelOption,
                orderName: data.orderName,
                customerName: data.customerName,
                customerAddress: data.customerAddress,
              }
            : undefined
        );
        break;
      case 'exchange-item-shipped':
        result = await sendExchangeItemShippedEmail(
          data.to,
          data.returnNumber,
          data.trackingNumber
        );
        break;
      case 'exchange-item-received':
        result = await sendExchangeItemReceivedEmail(
          data.to,
          data.returnNumber
        );
        break;
      case 'exchange-request-manual-received':
        result = await sendExchangeRequestManualReceivedEmail(
          data.to,
          data.returnNumber
        );
        break;
      case 'exchange-request-manual-approved':
        result = await sendExchangeRequestManualApprovedEmail(
          data.to,
          data.returnNumber,
          data.returnLabelOption && data.orderName
            ? {
                returnLabelOption: data.returnLabelOption,
                orderName: data.orderName,
                customerName: data.customerName,
                customerAddress: data.customerAddress,
              }
            : undefined
        );
        break;
      case 'request-declined':
        result = await sendRequestDeclinedEmail(data.to, data.returnNumber);
        break;
      default:
        return NextResponse.json(
          { success: false, error: 'Invalid email type' },
          { status: 400 }
        );
    }

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: 'Email sent successfully',
      });
    }
    return NextResponse.json(
      { success: false, error: 'Failed to send email' },
      { status: 500 }
    );
  } catch (error) {
    console.error('Email API error:', error);
    return NextResponse.json(
      { success: false, error: 'Invalid request data' },
      { status: 400 }
    );
  }
}

export const POST = withAuth(postHandler);
