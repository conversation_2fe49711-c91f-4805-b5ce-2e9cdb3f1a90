'use client';
import { Badge } from '@repo/design-system/components/ui/badge';
import { Button } from '@repo/design-system/components/ui/button';
import { useToast } from '@repo/design-system/components/ui/use-toast';
import { RotateCcw } from 'lucide-react';
import { useEffect, useState } from 'react';
import { retryJobAction } from '../actions';

interface Job {
  id: string;
  name: string;
  data: {
    type: string;
    to: string;
    returnNumber: string;
    priority?: number;
  };
  opts: any;
  progress: number;
  attemptsMade: number;
  finishedOn?: number;
  processedOn?: number;
  timestamp: number;
  returnvalue?: any;
  failedReason?: string;
}

export function EmailQueueJobs() {
  const [jobs, setJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState(true);
  const [retryingJob, setRetryingJob] = useState<string | null>(null);
  const { toast } = useToast();

  const fetchJobs = () => {
    try {
      // This would need to be implemented in the API
      // For now, we'll show a placeholder
      setJobs([]);
    } catch (error) {
      console.error('Failed to fetch jobs:', error);
    } finally {
      setLoading(false);
    }
  };

  const retryJob = async (jobId: string) => {
    setRetryingJob(jobId);

    try {
      const response = await retryJobAction(jobId);
      const result = await response.json();

      if (result.success) {
        toast({
          title: 'Success',
          description: `Job ${jobId} retried successfully`,
        });
        fetchJobs(); // Refresh the jobs list
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      toast({
        title: 'Error',
        description:
          error instanceof Error ? error.message : 'Failed to retry job',
        variant: 'destructive',
      });
    } finally {
      setRetryingJob(null);
    }
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    fetchJobs();
    const interval = setInterval(fetchJobs, 10000); // Refresh every 10 seconds
    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return <div>Loading jobs...</div>;
  }

  if (jobs.length === 0) {
    return (
      <div className="py-8 text-center text-muted-foreground">
        <p>No recent jobs found.</p>
        <p className="mt-2 text-sm">
          Jobs will appear here as emails are queued for processing.
        </p>
      </div>
    );
  }

  const getStatusBadge = (job: Job) => {
    if (job.failedReason) {
      return <Badge variant="destructive">Failed</Badge>;
    }
    if (job.finishedOn) {
      return <Badge variant="default">Completed</Badge>;
    }
    if (job.processedOn) {
      return <Badge variant="secondary">Processing</Badge>;
    }
    return <Badge variant="outline">Waiting</Badge>;
  };

  const formatEmailType = (type: string) => {
    return type
      .split('-')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  return (
    <div className="space-y-4">
      {jobs.map((job) => (
        <div
          key={job.id}
          className="flex items-center justify-between rounded-lg border p-4"
        >
          <div className="flex-1">
            <div className="mb-2 flex items-center space-x-2">
              {getStatusBadge(job)}
              <span className="font-medium">
                {formatEmailType(job.data.type)}
              </span>
              <span className="text-muted-foreground text-sm">
                #{job.data.returnNumber}
              </span>
            </div>
            <div className="text-muted-foreground text-sm">
              <p>To: {job.data.to}</p>
              <p>
                Created: {new Date(job.timestamp).toLocaleString()}
                {job.finishedOn && (
                  <span className="ml-4">
                    Finished: {new Date(job.finishedOn).toLocaleString()}
                  </span>
                )}
              </p>
              {job.attemptsMade > 0 && <p>Attempts: {job.attemptsMade}</p>}
              {job.failedReason && (
                <p className="text-destructive">Error: {job.failedReason}</p>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {job.data.priority && (
              <Badge variant="outline" className="text-xs">
                Priority: {job.data.priority}
              </Badge>
            )}
            {job.failedReason && (
              <Button
                size="sm"
                variant="outline"
                onClick={() => retryJob(job.id)}
                disabled={retryingJob === job.id}
              >
                <RotateCcw className="mr-1 h-4 w-4" />
                {retryingJob === job.id ? 'Retrying...' : 'Retry'}
              </Button>
            )}
          </div>
        </div>
      ))}
    </div>
  );
}
