'use client';

import type { GetOrdersQuery } from '@/types/admin.generated';
import {
  Alert,
  AlertDescription,
} from '@repo/design-system/components/ui/alert';
import type { Dictionary } from '@repo/internationalization';
import { AlertCircle, Calendar } from 'lucide-react';
import SelectedItemsDisplay from '../selected-items-display';
import type { FlowState } from '../types';

// Helper functions to get the appropriate messages based on return status
function getReturnStatusTitle(
  status: string | null | undefined,
  dictionary: Dictionary
): string {
  switch (status) {
    case 'rejected':
      return (
        dictionary.return?.already_rejected ||
        'These items have already been rejected for return.'
      );
    case 'pending':
      return (
        dictionary.return?.already_pending ||
        'These items already have a pending return request.'
      );
    case 'approved':
      return (
        dictionary.return?.already_approved ||
        'These items already have an approved return request.'
      );
    case 'completed':
      return (
        dictionary.return?.already_completed ||
        'These items have already been returned.'
      );
    default:
      return (
        dictionary.return?.already_processed ||
        dictionary.return?.already_processed_summary ||
        'These items have already been processed for return or exchange.'
      );
  }
}

function getReturnStatusDetails(
  status: string | null | undefined,
  dictionary: Dictionary
): string {
  switch (status) {
    case 'pending':
      return (
        dictionary.return?.pending_details ||
        'Your return request is currently being processed. Please check back later for updates.'
      );
    case 'approved':
      return (
        dictionary.return?.approved_details ||
        'Your return request has been approved. Please follow the instructions provided in your email.'
      );
    case 'completed':
      return (
        dictionary.return?.completed_details ||
        'These items have already been successfully returned and processed.'
      );
    default:
      return (
        dictionary.return?.already_processed_details ||
        'Our system shows that these items have already been processed for a return or exchange. Each item can only be returned once.'
      );
  }
}

interface ReturnNotAllowedStateProps {
  selectedItems: NonNullable<
    GetOrdersQuery['orders']['edges'][0]['node']
  >['lineItems']['edges'];
  order: NonNullable<GetOrdersQuery['orders']['edges'][0]['node']>;
  dictionary: Dictionary;
  flowState: FlowState;
}

export default function ReturnNotAllowedState({
  selectedItems,
  order,
  dictionary,
  flowState,
}: ReturnNotAllowedStateProps) {
  // Check if all items have been processed
  const allItemsProcessed =
    flowState.lineItemsProcessed &&
    selectedItems.every((item) =>
      flowState.lineItemsProcessed?.includes(item.node.id)
    );

  return (
    <div className="space-y-6">
      <SelectedItemsDisplay
        selectedItems={selectedItems}
        dictionary={dictionary}
      />

      {flowState.isExpired && (
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-5 w-5" />
          <AlertDescription className="ml-2">
            <p className="font-medium">
              {dictionary.return.expired_message.replace(
                '{days}',
                String(flowState.autoApprovalDays)
              )}
            </p>
            <p className="mt-1 text-sm">
              {dictionary.return.expired_policy.replace(
                '{days}',
                String(flowState.autoApprovalDays)
              )}
            </p>
          </AlertDescription>
        </Alert>
      )}

      {allItemsProcessed && (
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-5 w-5" />
          <AlertDescription className="ml-2">
            <p className="font-medium">
              {getReturnStatusTitle(flowState.existingReturnStatus, dictionary)}
            </p>
            <p className="mt-1 text-sm">
              {getReturnStatusDetails(
                flowState.existingReturnStatus,
                dictionary
              )}
            </p>
          </AlertDescription>
        </Alert>
      )}

      {/* Show order date information */}
      {flowState.orderDate && (
        <div className="mt-4 text-muted-foreground text-sm">
          <p className="flex items-center">
            <Calendar className="mr-2 h-4 w-4" />
            {order.fulfillments?.some(
              (fulfillment) =>
                fulfillment.status === 'SUCCESS' ||
                fulfillment.status === 'PENDING' ||
                fulfillment.status === 'OPEN'
            )
              ? dictionary.return?.delivery_date || 'Delivery Date: '
              : dictionary.return?.order_date || 'Order Date: '}
            {flowState.orderDate.toLocaleDateString()}
          </p>
          {order.fulfillments?.some(
            (fulfillment) =>
              fulfillment.status === 'SUCCESS' ||
              fulfillment.status === 'PENDING' ||
              fulfillment.status === 'OPEN'
          ) && (
            <p className="mt-1 ml-6 text-xs">
              {dictionary.return?.return_period_calculation ||
                'Return period is calculated from the delivery date'}
            </p>
          )}
        </div>
      )}

      <div className="mt-4 rounded-lg bg-muted p-4">
        <h4 className="mb-2 font-medium">
          {dictionary.return?.need_assistance || 'Need assistance?'}
        </h4>
        <p className="text-sm">
          {dictionary.return?.assistance_message ||
            'If you believe this is an error or have questions about our return policy, please contact our customer support team for assistance.'}
        </p>
      </div>
    </div>
  );
}
