import { env } from '@/env';
import { getLogisticsStatus } from '@/lib/logistics';
import { getOrderByName } from '@/lib/shopify';
import { log } from '@repo/observability/log';
import { type NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { orderName, email }: { orderName: string; email: string } =
      await request.json();

    log.info(`Checking if address change is allowed for order ${orderName}`);

    if (!orderName || !email) {
      return NextResponse.json(
        { error: 'Order name and email are required' },
        { status: 400 }
      );
    }

    // Verify the order with email
    const order = await getOrderByName(orderName, email);

    if (!order) {
      return NextResponse.json(
        {
          success: false,
          message:
            "Order not found or email doesn't match. Please check your information and try again.",
        },
        { status: 200 }
      );
    }

    // Get environment (Japan or global)
    const isJapanEnv = env.NEXT_PUBLIC_REGION === 'JP';

    const isFulfilled = order.fulfillments.some(
      (fulfillment) =>
        fulfillment.status === 'SUCCESS' ||
        fulfillment.status === 'PENDING' ||
        fulfillment.status === 'OPEN'
    );
    const hasTrackingNumber = isJapanEnv
      ? (await getLogisticsStatus(orderName)) >= 4 // already start packaging at 4
      : false;

    // For Japan environment, check against casefinite orders logistic status
    // For global environment, only check against fulfillment in shopify
    const canChangeAddress = isJapanEnv
      ? !hasTrackingNumber && !isFulfilled
      : !isFulfilled;

    return NextResponse.json(
      {
        success: true,
        canChangeAddress,
        hasTrackingNumber,
        isFulfilled,
        isJapanEnv,
        customerInfo: {
          firstName: order.shippingAddress?.firstName || '',
          lastName: order.shippingAddress?.lastName || '',
          phone: order.shippingAddress?.phone || '',
          country: order.shippingAddress?.country || '',
        },
      },
      { status: 200 }
    );
  } catch (error) {
    log.error('Error checking address change:', { error });
    return NextResponse.json(
      {
        error: 'An error occurred while checking if address change is allowed',
      },
      { status: 500 }
    );
  }
}
