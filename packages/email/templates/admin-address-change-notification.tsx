import {
  Body,
  Container,
  Head,
  Hr,
  Html,
  Img,
  Preview,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';
// biome-ignore lint/correctness/noUnusedImports: <explanation>
import React from 'react';

type AddressInfo = {
  readonly firstName?: string;
  readonly lastName?: string;
  readonly address1?: string;
  readonly address2?: string;
  readonly city?: string;
  readonly province?: string;
  readonly zip?: string;
  readonly country?: string;
  readonly phone?: string;
};

type CustomerInfo = {
  readonly firstName?: string;
  readonly lastName?: string;
  readonly email: string;
  readonly phone?: string;
};

type AdminAddressChangeNotificationProps = {
  readonly orderName: string;
  readonly customerInfo: CustomerInfo;
  readonly originalAddress: AddressInfo;
  readonly newAddress: AddressInfo;
};

const formatAddress = (address: AddressInfo): string => {
  const parts = [
    address.firstName && address.lastName
      ? `${address.firstName} ${address.lastName}`
      : '',
    address.address1 || '',
    address.address2 || '',
    [address.city, address.province, address.zip].filter(Boolean).join(', '),
    address.country || '',
    address.phone ? `Phone: ${address.phone}` : '',
  ].filter(Boolean);

  return parts.join('\n');
};

export const AdminAddressChangeNotificationTemplate = ({
  orderName,
  customerInfo,
  originalAddress,
  newAddress,
}: AdminAddressChangeNotificationProps) => (
  <Tailwind>
    <Html>
      <Head />
      <Preview>Address change request for order {orderName}</Preview>
      <Body className="bg-zinc-50 font-sans">
        <Container className="mx-auto py-12">
          <Img
            className="mx-auto"
            src="https://cdn.sanity.io/images/lpmcxine/jp_20250413/2805425d31d67c0c7268047a29b64203326c9a8f-272x68.png"
          />
          <Section className="mt-8 rounded-md bg-zinc-200 p-px">
            <Section className="rounded-[5px] bg-white p-8">
              <Text className="mt-0 mb-4 font-semibold text-2xl text-zinc-950">
                Address Change Request
              </Text>
              <Text className="m-0 mb-4 text-zinc-700">
                A customer has requested an address change for their order.
              </Text>

              <Hr className="my-4" />

              <Text className="m-0 mb-2 font-semibold text-lg text-zinc-950">
                Order Information
              </Text>
              <Text className="m-0 mb-1 text-zinc-700">
                <strong>Order ID:</strong> {orderName}
              </Text>
              <Text className="m-0 mb-4 text-zinc-700">
                <strong>Customer:</strong> {customerInfo.firstName}{' '}
                {customerInfo.lastName} ({customerInfo.email})
              </Text>

              <Hr className="my-4" />

              <Text className="m-0 mb-2 font-semibold text-lg text-zinc-950">
                Original Address
              </Text>
              <Text className="m-0 mb-4 whitespace-pre-line text-zinc-700">
                {formatAddress(originalAddress)}
              </Text>

              <Hr className="my-4" />

              <Text className="m-0 mb-2 font-semibold text-lg text-zinc-950">
                New Address
              </Text>
              <Text className="m-0 mb-4 whitespace-pre-line text-zinc-700">
                {formatAddress(newAddress)}
              </Text>

              <Hr className="my-4" />

              <Text className="m-0 text-sm text-zinc-500">
                This notification was automatically generated when the customer
                updated their shipping address. Please review the address change
                request in the admin panel.
              </Text>
            </Section>
          </Section>
        </Container>
      </Body>
    </Html>
  </Tailwind>
);

const ExampleAdminAddressChangeNotification = () => (
  <AdminAddressChangeNotificationTemplate
    orderName="#1001"
    customerInfo={{
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '******-123-4567',
    }}
    originalAddress={{
      firstName: 'John',
      lastName: 'Doe',
      address1: '123 Old Street',
      address2: 'Apt 4B',
      city: 'Old City',
      province: 'CA',
      zip: '90210',
      country: 'United States',
      phone: '******-123-4567',
    }}
    newAddress={{
      firstName: 'John',
      lastName: 'Doe',
      address1: '456 New Avenue',
      address2: 'Suite 10',
      city: 'New City',
      province: 'NY',
      zip: '10001',
      country: 'United States',
      phone: '******-987-6543',
    }}
  />
);

export default ExampleAdminAddressChangeNotification;
