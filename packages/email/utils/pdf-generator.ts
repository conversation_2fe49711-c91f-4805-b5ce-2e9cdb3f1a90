import type { EmailAttachment } from '../send';

export interface ReturnLabelData {
  orderName: string;
  returnNumber: string;
  customerName?: string;
  customerAddress?: string;
  companyName?: string;
  companyAddress?: string;
  returnDepartment?: string;
}

/**
 * Client-safe wrapper for PDF generation
 * This function conditionally imports the server-only PDF generator package
 */
export async function generateReturnLabelPDF(
  data: ReturnLabelData
): Promise<EmailAttachment> {
  // Check if we're in a browser environment
  if (typeof window !== 'undefined') {
    throw new Error('PDF generation is not supported in browser environments');
  }

  // Check if we're in a Node.js environment
  if (typeof process === 'undefined' || !process.versions?.node) {
    throw new Error('PDF generation requires a Node.js environment');
  }

  // Dynamically import the server-only PDF generator package
  // This prevents the canvas dependency from being bundled in client builds
  try {
    // Use eval to prevent webpack from trying to resolve this at build time
    const moduleName = '@repo/pdf-generator';
    const pdfModule = await import(/* webpackIgnore: true */ moduleName);
    return await pdfModule.generateReturnLabelPDF(data);
  } catch (error) {
    console.error('Error loading server PDF generator:', error);
    throw new Error('Failed to load PDF generation module');
  }
}
