{"name": "@repo/pdf-generator", "version": "0.0.0", "private": true, "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "dependencies": {"canvas": "^2.11.2", "jsbarcode": "^3.11.6", "jspdf": "^3.0.1"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "typescript": "^5.7.3"}, "scripts": {"build": "tsc", "clean": "rm -rf dist", "dev": "tsc --watch", "type-check": "tsc --noEmit"}}