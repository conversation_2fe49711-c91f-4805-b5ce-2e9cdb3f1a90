export interface EmailAttachment {
  content: string; // base64 encoded content
  filename: string;
  type: string; // MIME type
  disposition: 'attachment' | 'inline';
}

export interface ReturnLabelData {
  orderName: string;
  returnNumber: string;
  customerName?: string;
  customerAddress?: string;
  companyName?: string;
  companyAddress?: string;
  returnDepartment?: string;
}

/**
 * Server-only PDF generation function
 * This package should only be used in server environments
 */
export async function generateReturnLabelPDF(
  data: ReturnLabelData
): Promise<EmailAttachment> {
  // This function should only run on the server
  if (typeof window !== 'undefined') {
    throw new Error('PDF generation is not supported in browser environments');
  }

  if (typeof process === 'undefined' || !process.versions?.node) {
    throw new Error('PDF generation requires a Node.js environment');
  }

  try {
    // Import the dependencies
    const { jsPDF } = await import('jspdf');
    const JsBarcode = (await import('jsbarcode')).default;
    const { createCanvas } = await import('canvas');

    // Create a new PDF document
    const doc = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4',
    });

    // Set up the document
    doc.setFontSize(16);
    doc.text('Return Label', 105, 20, { align: 'center' });

    // Add order information
    doc.setFontSize(12);
    doc.text(`Order: ${data.orderName}`, 20, 40);

    if (data.customerName) {
      doc.text(`Customer: ${data.customerName}`, 20, 50);
    }

    if (data.customerAddress) {
      doc.text(`Address: ${data.customerAddress}`, 20, 60);
    }

    // Generate barcode using canvas
    const canvas = createCanvas(400, 100);

    // Generate barcode on canvas
    JsBarcode(canvas as any, data.returnNumber, {
      format: 'CODE128',
      width: 2,
      height: 50,
      displayValue: true,
    });

    // Add the barcode to the PDF
    const barcodeDataUrl = canvas.toDataURL('image/png');
    doc.addImage(barcodeDataUrl, 'PNG', 50, 80, 100, 30);

    // Add company address and return instructions
    doc.setFontSize(10);
    doc.text('Return to:', 20, 130);
    doc.text(data.companyName || 'Casefinite JP', 20, 140);
    if (data.returnDepartment) {
      doc.text(data.returnDepartment, 20, 145);
    }
    doc.text(data.companyAddress || 'Casefinite Address', 20, 150);

    // Add instructions
    doc.setFontSize(11);
    doc.text('Instructions:', 20, 170);
    doc.text('1. Cut along the dotted line', 25, 180);
    doc.text('2. Attach this label to your package', 25, 185);
    doc.text('3. Drop off at your nearest postal service location', 25, 190);

    // Get PDF as base64 string
    const pdfBase64 = doc.output('datauristring').split(',')[1];

    return {
      content: pdfBase64,
      filename: `return-label-${data.orderName.replace('#', '')}.pdf`,
      type: 'application/pdf',
      disposition: 'attachment',
    };
  } catch (error) {
    console.error('Error generating return label PDF:', error);
    throw new Error('Failed to generate return label PDF');
  }
}
